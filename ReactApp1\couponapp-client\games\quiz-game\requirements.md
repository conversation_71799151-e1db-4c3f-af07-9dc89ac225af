# Quiz Game Flow Requirements

## Overview
The quiz game supports different configurations that affect the game flow:
- Lives system (enabled/disabled)
- Rewards system (enabled/disabled)

## Configuration Options

### Lives System
- **useLives**: boolean - Whether to limit play attempts using lives
- **livesCount**: number - Number of lives (default: 3)

### Rewards System  
- **rewardsEnabled**: boolean - Whether rewards are enabled
- **enableCtaButton**: boolean - Whether to show CTA button on reward screen
- **showRewardInGame**: boolean - Whether to show reward info during gameplay

## Game Flow Scenarios

### 1. Lives Enabled + Rewards Enabled
**Flow:**
1. Start Screen (optional) → Main Game
2. Player answers questions
3. On wrong answer:
   - Lose 1 life
   - Check reward roll result
   - If won reward → Reward Screen → Game Finished
   - If no reward and lives > 0 → Continue playing
   - If no reward and lives = 0 → Out of Lives Screen → Game Finished
4. On completing all questions → Game Finished

**Screens:** Start → Main → Reward/Out of Lives

### 2. Lives Enabled + Rewards Disabled
**Flow:**
1. Start Screen (optional) → Main Game
2. Player answers questions
3. On wrong answer:
   - Lose 1 life
   - If lives > 0 → Continue playing
   - If lives = 0 → Out of Lives Screen → Game Finished
4. On completing all questions → Game Finished

**Screens:** Start → Main → Out of Lives

### 3. Lives Disabled + Rewards Enabled
**Flow:**
1. Start Screen (optional) → Main Game
2. Player answers questions (unlimited attempts)
3. On wrong answer:
   - Check reward roll result
   - If won reward → Reward Screen → Game Finished
   - If no reward → Continue playing
4. On completing all questions → Game Finished

**Screens:** Start → Main → Reward

### 4. Lives Disabled + Rewards Disabled
**Flow:**
1. Start Screen (optional) → Main Game
2. Player answers questions (unlimited attempts)
3. On wrong answer → Continue playing
4. On completing all questions → Game Finished

**Screens:** Start → Main

## Screen Definitions

### Start Screen
- **Purpose**: Game introduction and start button
- **Trigger**: Game initialization (if enabled)
- **Actions**: Start game button → Main Game

### Main Game Screen
- **Purpose**: Quiz gameplay with questions and answers
- **Components**: Question, answer options, timer, score, lives counter
- **Actions**: Answer selection, continue to next question

### Try Again Screen
- **Purpose**: Intermediate screen after losing life (currently unused in quiz)
- **Note**: Quiz game continues directly without showing this screen

### Reward Screen
- **Purpose**: Display won reward and claim button
- **Trigger**: Player wins reward after wrong answer
- **Actions**: Claim reward → Game Finished

### Out of Lives Screen
- **Purpose**: Game over when no lives remaining
- **Trigger**: Lives = 0 after wrong answer
- **Actions**: Game over → Game Finished

## Game State Management

### Lives System
- Initial lives set from config (default: 3)
- Lives decremented on wrong answers (if enabled)
- Lives = MAX_SAFE_INTEGER when disabled

### Score System
- Current score (resets each round)
- Best score (persistent)
- Attempts taken counter

### Round System
- Round ID for reward tracking
- New round generated on restart

## Events

### Game Events
- **round_start**: Triggered when new round begins
- **GameFinished**: Triggered when game ends (any scenario)

### Reward Events
- Reward roll happens at round start
- Reward check on wrong answers
- Reward claim on reward screen

## Additional Cases to Consider

### Timer System
- **useTimer**: boolean - Whether to use question timer
- **timerDuration**: number - Seconds per question
- **loseLifeOnTimeout**: boolean - Whether timeout counts as wrong answer

### Timer Flow Impact
- When timer expires → treated as wrong answer
- Follows same flow as wrong answer based on lives/rewards config

### Edge Cases
1. **Last question + wrong answer + reward won**: Show reward screen
2. **Last question + wrong answer + no reward + lives > 0**: Game finished
3. **Last question + correct answer**: Game finished
4. **Timer timeout**: Same as wrong answer flow

## Technical Notes

### Screen Navigation
- Screens controlled by `currentScreenId` state
- Navigation handled by button click handlers
- Preview mode bypasses most game logic

### Widget Slots
- `outOfLives`: Game over content
- `tryAgain`: Try again content (unused)
- `gameOver`: General game over content
- `rewardScreen`: Reward display content

### Configuration Dependencies
- Lives system independent of rewards
- Rewards can work with or without lives
- Timer system works with both configurations

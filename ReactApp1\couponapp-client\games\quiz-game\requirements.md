# Quiz Game Flow Requirements

## Game Flow Cases

### Lives ON + Rewards ON
Wrong answer → Check reward → If won: Reward Screen → End | If lost: Lose life → If lives=0: Game Over → End

### Lives ON + Rewards OFF
Wrong answer → Lose life → If lives=0: Game Over → End

### Lives OFF + Rewards ON
Wrong answer → Check reward → If won: Reward Screen → End | If lost: Continue

### Lives OFF + Rewards OFF
Wrong answer → Continue

## Screens
- **Start**: Optional intro
- **Main**: Quiz gameplay
- **Reward**: Show won reward
- **Out of Lives**: Game over when lives=0
